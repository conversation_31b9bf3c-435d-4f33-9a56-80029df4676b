{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "cc5e2dad8ed5c93ff3fd9afc574bee12", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "271276077e0754e175990be64888c865e3873924378fb4f788bcf31faab9b719", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "232efa7aae10debab2d8911b47c8f432474886987f6cd81ec19bdaed941c6e54"}}}, "functions": {}, "sortedMiddleware": ["/"]}