{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "1af54761ef2a2719687812d2067f550b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4673b9e11a22c8d47c038021534754656da57d6d7dab42949aa4f99c9b30ebfb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "11c02b622f53565dec2e52d84e73c4ec1c1c9f413ab544692e8d2cdbd19045aa"}}}, "functions": {}, "sortedMiddleware": ["/"]}