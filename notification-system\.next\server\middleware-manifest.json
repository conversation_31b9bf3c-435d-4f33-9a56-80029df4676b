{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "7abd60126be9e33827b9d7a10454dd89", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cba678621d7d1f8e49b362a5dd4332dcaf1bb64117c2ad5dcf595419ccbee651", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "734dc245f7b36b5ae0e1446c7702cbe021f8d7ae1e7ca313604a7acb3adf5e3a"}}}, "functions": {}, "sortedMiddleware": ["/"]}