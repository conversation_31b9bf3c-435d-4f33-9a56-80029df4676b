{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "6fd7c3d34ebcb491b7bc7ea09585d444", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ccb0763ccbf3a67d77413ddc2ae3d4a80a0defcfa1f8acfeefcefc9af183f653", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5f75f4ebb311c4de0b051c5b36f47980a1352b31f6e0d116eb82d53f520fe231"}}}, "functions": {}, "sortedMiddleware": ["/"]}