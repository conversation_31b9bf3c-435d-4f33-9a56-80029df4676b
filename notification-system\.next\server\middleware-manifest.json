{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "6e4de6acf7d88df33f369384a2ef5b4c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fa86755c1c009e535658c04679cc3258362382b7599c90947930be293e340548", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "79130955d4edeebff51f52dfd9b84ac1896ed87e97b993ce06d3e9bb3da8960d"}}}, "functions": {}, "sortedMiddleware": ["/"]}