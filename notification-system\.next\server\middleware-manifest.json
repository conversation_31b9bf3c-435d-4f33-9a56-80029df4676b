{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "387bfb742b33825e7c89eb4d9b8258f6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8c97f4b2c35273a2ba98a50d73ee464c789084d0913dad59ba78a227cbd70ff2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9d4070c55c335cf952c10dac51ec3688fa435c12a9eb27ff007a0064b3d0f2f0"}}}, "functions": {}, "sortedMiddleware": ["/"]}