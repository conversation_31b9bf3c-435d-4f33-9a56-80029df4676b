{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "293e33d24a27977a8e002c14d652b26a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "837a2f8c2be07131e1a22bcaa498d33096adc2cf73784b259affb15b51826d7d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c1c7eb4a40db0524971b1818940191d3ce96cf53540de9e7a3f7991752e79022"}}}, "functions": {}, "sortedMiddleware": ["/"]}