{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "34fce3c17341d090173cbc950f48edf7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c35922d518128eced8bcdf904c21f86a80e8b783683c2d284cbb8f197843cff7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ff33ad46cb7ce8154cb2a3fcb9435cbf852246431168a895a5d7a28dc1112096"}}}, "functions": {}, "sortedMiddleware": ["/"]}