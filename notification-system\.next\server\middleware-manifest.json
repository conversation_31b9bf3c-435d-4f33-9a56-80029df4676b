{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "a316ce4815b9737dce60e82694fbbac6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1f6538005cbb3ea5f6ebc89faa42eb822f44711a069f8d05d319fd9920e0595d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4d1f69ce7a204e52db2a47ad836bc58cce4d713ef0597558b1cea009e32a607f"}}}, "functions": {}, "sortedMiddleware": ["/"]}