{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "966d1ba078da03d7336fa21fed87b290", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5d52672071d651956fc8e66bffa5d01ace3b29550d8e2a6868ba09120eb11eeb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b1db69e94d0db5348472b2b3a5638b1f0aefdded56e6ef0ee209a70e53d0c33b"}}}, "functions": {}, "sortedMiddleware": ["/"]}