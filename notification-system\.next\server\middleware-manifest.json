{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "595503e28ed980689621ce3737326c31", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8bb12b299bba0c445787fcc3c3639909b805b7b4baf0b8da82ed62350e468e77", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b389f32f4c86b897c8f70b1b3d1746091512727d83ddaee469db1185d39c6b09"}}}, "functions": {}, "sortedMiddleware": ["/"]}