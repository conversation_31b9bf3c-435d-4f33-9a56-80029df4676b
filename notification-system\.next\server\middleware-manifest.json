{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "79485e593514b0aba96ecc9b6831d9ae", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9575194d7c132637eb30f1525aca69fbfb47bae744daaebe5526f3b0c9285d94", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0a865e6155bf7c7ea1f30a785a1ddfc684e748e10fe20bcbaa4a6df547c1b48c"}}}, "functions": {}, "sortedMiddleware": ["/"]}