{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eHeVrqfNIfAn81OmSCEdukbjgEW++6+QtxX43uUPI34=", "__NEXT_PREVIEW_MODE_ID": "3282e9a0084620716efb3ee4f03e8445", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "55890ba4615a85b86d9765f51bb9a03d337977eec9ad8f5aa14899be955ea49b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c873031a84ee615a7a450211696f5ce4149e7d0da10c157e6aeff9e57a918711"}}}, "functions": {}, "sortedMiddleware": ["/"]}