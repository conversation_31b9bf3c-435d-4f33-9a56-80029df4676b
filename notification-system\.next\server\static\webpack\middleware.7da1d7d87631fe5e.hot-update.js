"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    let supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://pubxgqyxjuyyywwgqxgk.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1YnhncXl4anV5eXl3d2dxeGdrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU1NTEwNDYsImV4cCI6MjA3MTEyNzA0Nn0.ux1e7gAqLf3Jg-yr47auoDKMuGqSnPRV0-SKgHe8kQE\", {\n        cookies: {\n            getAll () {\n                return request.cookies.getAll();\n            },\n            setAll (cookiesToSet) {\n                cookiesToSet.forEach(({ name, value })=>request.cookies.set(name, value));\n                supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request\n                });\n                cookiesToSet.forEach(({ name, value, options })=>supabaseResponse.cookies.set(name, value, options));\n            }\n        }\n    });\n    // Refresh session if expired - required for Server Components\n    const { data: { user }, error } = await supabase.auth.getUser();\n    const { pathname } = request.nextUrl;\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/auth/login',\n        '/',\n        '/auth/register',\n        '/setup',\n        '/debug',\n        '/test-connection'\n    ];\n    // Check if current path is public\n    const isPublicRoute = publicRoutes.includes(pathname);\n    // If user is not authenticated and trying to access protected route\n    if (!user && !isPublicRoute) {\n        const redirectUrl = request.nextUrl.clone();\n        redirectUrl.pathname = '/auth/login';\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // If user is authenticated, get their role and check access\n    if (user && !isPublicRoute) {\n        const { data: userData } = await supabase.from('users').select('role, is_active').eq('id', user.id).single();\n        if (!userData?.is_active) {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/auth/login';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        const userRole = userData?.role;\n        // Role-based route protection\n        if (pathname.startsWith('/admin') && userRole !== 'admin') {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/unauthorized';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        if (pathname.startsWith('/student') && userRole !== 'student') {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/unauthorized';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        if (pathname.startsWith('/employee') && userRole !== 'employee') {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/unauthorized';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    // If user is authenticated and trying to access login page, redirect to dashboard\n    if (user && pathname === '/auth/login') {\n        const { data: userData } = await supabase.from('users').select('role').eq('id', user.id).single();\n        const redirectUrl = request.nextUrl.clone();\n        switch(userData?.role){\n            case 'admin':\n                redirectUrl.pathname = '/admin';\n                break;\n            case 'student':\n                redirectUrl.pathname = '/student';\n                break;\n            case 'employee':\n                redirectUrl.pathname = '/employee';\n                break;\n            default:\n                redirectUrl.pathname = '/';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    return supabaseResponse;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */ '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});