"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    let supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://pubxgqyxjuyyywwgqxgk.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1YnhncXl4anV5eXl3d2dxeGdrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU1NTEwNDYsImV4cCI6MjA3MTEyNzA0Nn0.ux1e7gAqLf3Jg-yr47auoDKMuGqSnPRV0-SKgHe8kQE\", {\n        cookies: {\n            getAll () {\n                return request.cookies.getAll();\n            },\n            setAll (cookiesToSet) {\n                cookiesToSet.forEach(({ name, value })=>request.cookies.set(name, value));\n                supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request\n                });\n                cookiesToSet.forEach(({ name, value, options })=>supabaseResponse.cookies.set(name, value, options));\n            }\n        }\n    });\n    // Refresh session if expired - required for Server Components\n    const { data: { user }, error } = await supabase.auth.getUser();\n    const { pathname } = request.nextUrl;\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/auth/login',\n        '/',\n        '/auth/register',\n        '/setup',\n        '/debug',\n        '/test-connection',\n        '/test-login'\n    ];\n    // Check if current path is public\n    const isPublicRoute = publicRoutes.includes(pathname);\n    // If user is not authenticated and trying to access protected route\n    if (!user && !isPublicRoute) {\n        const redirectUrl = request.nextUrl.clone();\n        redirectUrl.pathname = '/auth/login';\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // If user is authenticated, get their role and check access\n    if (user && !isPublicRoute) {\n        const { data: userData } = await supabase.from('users').select('role, is_active').eq('id', user.id).single();\n        if (!userData?.is_active) {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/auth/login';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        const userRole = userData?.role;\n        // Role-based route protection\n        if (pathname.startsWith('/admin') && userRole !== 'admin') {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/unauthorized';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        if (pathname.startsWith('/student') && userRole !== 'student') {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/unauthorized';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        if (pathname.startsWith('/employee') && userRole !== 'employee') {\n            const redirectUrl = request.nextUrl.clone();\n            redirectUrl.pathname = '/unauthorized';\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    // If user is authenticated and trying to access login page, redirect to dashboard\n    if (user && pathname === '/auth/login') {\n        const { data: userData } = await supabase.from('users').select('role').eq('id', user.id).single();\n        const redirectUrl = request.nextUrl.clone();\n        switch(userData?.role){\n            case 'admin':\n                redirectUrl.pathname = '/admin';\n                break;\n            case 'student':\n                redirectUrl.pathname = '/student';\n                break;\n            case 'employee':\n                redirectUrl.pathname = '/employee';\n                break;\n            default:\n                redirectUrl.pathname = '/';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    return supabaseResponse;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */ '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});