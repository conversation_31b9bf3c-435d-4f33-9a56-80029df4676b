# Notification System

A comprehensive mobile-first notification system built with Next.js and Supabase, designed for educational institutions to manage communications between administrators, students, and employees.

## Features

### 🔐 Role-Based Access Control
- **Admin Dashboard**: Full system management, user import, notification creation
- **Student Dashboard**: View received notifications, profile management
- **Employee Dashboard**: View received notifications, profile management

### 📢 Advanced Notification System
- **Targeted Messaging**: Send notifications to specific groups
- **Bulk Attachments**: Support for multiple file attachments
- **Recipient Groups**:
  - All users (students + employees)
  - All students
  - Students by class, level, specialization
  - All employees
  - Employees by contract type, job status, employee type

### 📊 Excel Import System
- **Student Import**: Bulk import with headers: `username`, `full_name`, `nationality`, `phone`, `specialization`, `section`, `class`, `level`, `result`, `password`, `is_active`
- **Employee Import**: Bulk import with headers: `full_name`, `contract_type`, `employee_type`, `phone`, `job_status`, `automatic_number`, `financial_number`, `state_cooperative_number`, `bank_account_number`, `username`, `password`, `is_active`

### 📱 Mobile-First Design
- Progressive Web App (PWA) capabilities
- Responsive design for all screen sizes
- Real-time notifications
- Offline support

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **File Processing**: xlsx library for Excel imports
- **Icons**: Lucide React
- **Notifications**: React Hot Toast

## Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### 1. Clone and Install
```bash
git clone <repository-url>
cd notification-system
npm install
```

### 2. Environment Setup
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. Database Setup
1. Create a new Supabase project
2. Run the SQL schema from `database/schema.sql` in your Supabase SQL editor
3. Enable Row Level Security (RLS) policies

### 4. Run Development Server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Database Schema

### Core Tables
- **users**: Base user information with role-based access
- **students**: Extended student-specific data
- **employees**: Extended employee-specific data
- **notifications**: Notification content and metadata
- **notification_recipients**: Tracks delivery and read status
- **notification_attachments**: File attachments for notifications
- **recipient_groups**: Defines notification targeting rules

### User Roles
- `admin`: Full system access
- `student`: Limited to own notifications and profile
- `employee`: Limited to own notifications and profile

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard pages
│   ├── student/           # Student dashboard pages
│   ├── employee/          # Employee dashboard pages
│   └── auth/              # Authentication pages
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   └── dashboard/        # Dashboard-specific components
├── lib/                  # Utility libraries
│   ├── supabase/         # Supabase client configuration
│   └── utils.ts          # Helper functions
└── types/                # TypeScript type definitions
```

## Development Guidelines

### Code Style
- Use TypeScript for all new files
- Follow Next.js App Router conventions
- Use Tailwind CSS for styling
- Implement proper error handling

### Security
- All database operations use Row Level Security (RLS)
- File uploads are validated for type and size
- User authentication required for all protected routes
- Input validation on both client and server

### Performance
- Server-side rendering for initial page loads
- Client-side navigation for subsequent pages
- Optimized images and assets
- Database indexes for common queries

## Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment
1. Build the application: `npm run build`
2. Start production server: `npm start`
3. Configure reverse proxy (nginx/Apache)

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
