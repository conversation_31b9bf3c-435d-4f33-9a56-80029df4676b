-- Fix the users table to make password_hash optional
-- Run this in Supabase SQL Editor

-- First, update existing records to have a placeholder
UPDATE public.users 
SET password_hash = 'supabase_auth' 
WHERE password_hash IS NULL OR password_hash = '';

-- Then make the column nullable
ALTER TABLE public.users 
ALTER COLUMN password_hash DROP NOT NULL;

-- Set default value for new records
ALTER TABLE public.users 
ALTER COLUMN password_hash SET DEFAULT 'supabase_auth';

-- Verify the change
SELECT column_name, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'users' AND column_name = 'password_hash';
