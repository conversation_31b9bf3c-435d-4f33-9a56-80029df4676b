-- Notification System Database Schema
-- This schema supports role-based access for admin, students, and employees
-- with comprehensive notification management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User roles enum
CREATE TYPE user_role AS ENUM ('admin', 'student', 'employee');

-- Contract types for employees
CREATE TYPE contract_type AS ENUM ('full_time', 'part_time', 'contract', 'temporary');

-- Employee types
CREATE TYPE employee_type AS ENUM ('academic', 'administrative', 'technical', 'support');

-- Job status for employees
CREATE TYPE job_status AS ENUM ('active', 'inactive', 'on_leave', 'terminated');

-- Users table (unified for all user types)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    phone VARCHAR(20),
    role user_role NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Students table (extends users)
CREATE TABLE students (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    nationality VARCHAR(100),
    specialization VARCHAR(100),
    section VARCHAR(50),
    class VARCHAR(50),
    level VARCHAR(20),
    result VARCHAR(50),
    student_number VARCHAR(50) UNIQUE,
    enrollment_date DATE DEFAULT CURRENT_DATE
);

-- Employees table (extends users)
CREATE TABLE employees (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    contract_type contract_type,
    employee_type employee_type,
    job_status job_status DEFAULT 'active',
    automatic_number VARCHAR(50),
    financial_number VARCHAR(50),
    state_cooperative_number VARCHAR(50),
    bank_account_number VARCHAR(100),
    employee_number VARCHAR(50) UNIQUE,
    hire_date DATE DEFAULT CURRENT_DATE,
    department VARCHAR(100)
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Notification attachments table
CREATE TABLE notification_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification recipients table (tracks who should receive each notification)
CREATE TABLE notification_recipients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(notification_id, user_id)
);

-- Recipient groups table (for bulk notification targeting)
CREATE TABLE recipient_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    group_type VARCHAR(50) NOT NULL, -- 'all', 'students', 'employees', 'by_class', 'by_level', 'by_contract_type', 'by_job_status'
    group_value VARCHAR(100), -- specific value for filtered groups (e.g., class name, level, etc.)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_students_class ON students(class);
CREATE INDEX idx_students_level ON students(level);
CREATE INDEX idx_students_specialization ON students(specialization);
CREATE INDEX idx_employees_contract_type ON employees(contract_type);
CREATE INDEX idx_employees_job_status ON employees(job_status);
CREATE INDEX idx_employees_employee_type ON employees(employee_type);
CREATE INDEX idx_notifications_created_by ON notifications(created_by);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notification_recipients_user_id ON notification_recipients(user_id);
CREATE INDEX idx_notification_recipients_notification_id ON notification_recipients(notification_id);
CREATE INDEX idx_notification_recipients_is_read ON notification_recipients(is_read);

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_recipients ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for notifications
CREATE POLICY "Admins can manage all notifications" ON notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Users can view notifications sent to them" ON notifications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM notification_recipients nr
            WHERE nr.notification_id = id AND nr.user_id = auth.uid()
        )
    );

-- RLS Policies for notification recipients
CREATE POLICY "Users can view their own notification status" ON notification_recipients
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own read status" ON notification_recipients
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all notification recipients" ON notification_recipients
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
