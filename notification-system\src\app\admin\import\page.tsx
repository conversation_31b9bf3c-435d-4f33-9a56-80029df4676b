import { requireAdmin } from '@/lib/auth'
import DashboardLayout from '@/components/layout/DashboardLayout'
import ExcelImport from '@/components/admin/ExcelImport'

export default async function ImportPage() {
  const user = await requireAdmin()

  return (
    <DashboardLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h1 className="text-2xl font-bold text-gray-900">Import Data</h1>
            <p className="mt-1 text-sm text-gray-600">
              Bulk import students and employees from Excel files
            </p>
          </div>
        </div>

        {/* Import Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Student Import */}
          <ExcelImport 
            type="students" 
            onImportComplete={(result) => {
              console.log('Student import completed:', result)
              // You can add additional logic here like refreshing data
            }}
          />

          {/* Employee Import */}
          <ExcelImport 
            type="employees" 
            onImportComplete={(result) => {
              console.log('Employee import completed:', result)
              // You can add additional logic here like refreshing data
            }}
          />
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-4">Import Instructions</h3>
          <div className="space-y-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium">File Format Requirements:</h4>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Excel files (.xlsx or .xls) only</li>
                <li>First row must contain the exact header names as shown above</li>
                <li>All required fields must be filled</li>
                <li>Maximum file size: 10MB</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">Data Validation:</h4>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Usernames must be unique across the system</li>
                <li>Phone numbers should be in valid format</li>
                <li>Boolean fields (is_active) accept: true/false, 1/0, yes/no</li>
                <li>Passwords will be hashed automatically</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">Error Handling:</h4>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Invalid rows will be skipped and reported</li>
                <li>Duplicate usernames will cause import failure for that row</li>
                <li>Partial imports are possible - valid rows will be imported even if some fail</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
