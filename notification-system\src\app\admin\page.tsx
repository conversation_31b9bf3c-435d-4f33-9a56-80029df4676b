import { requireAdmin } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Users, UserCheck, Briefcase, Bell, TrendingUp } from 'lucide-react'

async function getDashboardStats() {
  const supabase = createClient()
  
  // Get total users count
  const { count: totalUsers } = await supabase
    .from('users')
    .select('*', { count: 'exact', head: true })
  
  // Get students count
  const { count: totalStudents } = await supabase
    .from('users')
    .select('*', { count: 'exact', head: true })
    .eq('role', 'student')
  
  // Get employees count
  const { count: totalEmployees } = await supabase
    .from('users')
    .select('*', { count: 'exact', head: true })
    .eq('role', 'employee')
  
  // Get total notifications count
  const { count: totalNotifications } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
  
  // Get recent notifications
  const { data: recentNotifications } = await supabase
    .from('notifications')
    .select(`
      id,
      title,
      message,
      created_at,
      created_by,
      users!notifications_created_by_fkey(full_name)
    `)
    .order('created_at', { ascending: false })
    .limit(5)
  
  return {
    totalUsers: totalUsers || 0,
    totalStudents: totalStudents || 0,
    totalEmployees: totalEmployees || 0,
    totalNotifications: totalNotifications || 0,
    recentNotifications: recentNotifications || []
  }
}

export default async function AdminDashboard() {
  const user = await requireAdmin()
  const stats = await getDashboardStats()

  const statCards = [
    {
      name: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: 'Students',
      value: stats.totalStudents,
      icon: UserCheck,
      color: 'bg-green-500',
      change: '+8%',
      changeType: 'increase'
    },
    {
      name: 'Employees',
      value: stats.totalEmployees,
      icon: Briefcase,
      color: 'bg-purple-500',
      change: '+3%',
      changeType: 'increase'
    },
    {
      name: 'Notifications',
      value: stats.totalNotifications,
      icon: Bell,
      color: 'bg-orange-500',
      change: '+15%',
      changeType: 'increase'
    }
  ]

  return (
    <DashboardLayout user={user}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back, {user.full_name}!
            </h1>
            <p className="text-gray-600">
              Here's what's happening with your notification system today.
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {statCards.map((stat) => (
            <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${stat.color} rounded-md p-3`}>
                      <stat.icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          {stat.value.toLocaleString()}
                        </div>
                        <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                          <TrendingUp className="h-4 w-4 mr-1" />
                          {stat.change}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Notifications */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Recent Notifications
              </h3>
              <div className="space-y-4">
                {stats.recentNotifications.length > 0 ? (
                  stats.recentNotifications.map((notification: any) => (
                    <div key={notification.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="bg-blue-100 rounded-full p-2">
                          <Bell className="h-4 w-4 text-blue-600" />
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {notification.title}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          By {notification.users?.full_name} • {new Date(notification.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No notifications yet</p>
                )}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Quick Actions
              </h3>
              <div className="space-y-3">
                <a
                  href="/admin/notifications/create"
                  className="block w-full text-left px-4 py-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                >
                  <div className="flex items-center">
                    <Bell className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-blue-900">Send Notification</p>
                      <p className="text-xs text-blue-700">Create and send a new notification</p>
                    </div>
                  </div>
                </a>
                
                <a
                  href="/admin/import"
                  className="block w-full text-left px-4 py-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
                >
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-green-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-green-900">Import Users</p>
                      <p className="text-xs text-green-700">Bulk import students or employees</p>
                    </div>
                  </div>
                </a>
                
                <a
                  href="/admin/users"
                  className="block w-full text-left px-4 py-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
                >
                  <div className="flex items-center">
                    <UserCheck className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-purple-900">Manage Users</p>
                      <p className="text-xs text-purple-700">View and manage all users</p>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
