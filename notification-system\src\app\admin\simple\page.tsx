export default function SimpleAdminPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          🎉 Admin Dashboard - Working!
        </h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Welcome Administrator!</h2>
          <p className="text-gray-600 mb-4">
            Your notification system is now working. Here's what you can do:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">📝 Create Notifications</h3>
              <p className="text-blue-700 text-sm">Send notifications to students and employees</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">👥 Manage Users</h3>
              <p className="text-green-700 text-sm">Import and manage student/employee data</p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">📊 View Analytics</h3>
              <p className="text-purple-700 text-sm">Track notification delivery and engagement</p>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <h3 className="font-semibold text-orange-900 mb-2">⚙️ System Settings</h3>
              <p className="text-orange-700 text-sm">Configure system preferences</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="space-y-3">
            <a 
              href="/admin" 
              className="block w-full text-left px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              🏠 Go to Full Dashboard
            </a>
            <a 
              href="/admin/users" 
              className="block w-full text-left px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              👥 Manage Users
            </a>
            <a 
              href="/admin/import" 
              className="block w-full text-left px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              📤 Import Data
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
