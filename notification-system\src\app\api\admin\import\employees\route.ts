import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { processEmployeeExcel } from '@/lib/excel'
import { requireAdmin } from '@/lib/auth'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    await requireAdmin()
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }
    
    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an Excel file (.xlsx or .xls)' },
        { status: 400 }
      )
    }
    
    // Process Excel file
    const result = await processEmployeeExcel(file)
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Failed to process Excel file',
          details: result.errors
        },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const importedEmployees = []
    const errors = []
    
    // Process each employee
    for (const employeeData of result.data) {
      try {
        // Hash password
        const hashedPassword = await bcrypt.hash(employeeData.password, 12)
        
        // Create user record
        const { data: userData, error: userError } = await supabase
          .from('users')
          .insert({
            username: employeeData.username,
            full_name: employeeData.full_name,
            phone: employeeData.phone,
            password_hash: hashedPassword,
            role: 'employee',
            is_active: employeeData.is_active
          })
          .select()
          .single()
        
        if (userError) {
          errors.push(`Failed to create user ${employeeData.username}: ${userError.message}`)
          continue
        }
        
        // Create employee record
        const { error: employeeError } = await supabase
          .from('employees')
          .insert({
            id: userData.id,
            contract_type: employeeData.contract_type,
            employee_type: employeeData.employee_type,
            job_status: employeeData.job_status,
            automatic_number: employeeData.automatic_number,
            financial_number: employeeData.financial_number,
            state_cooperative_number: employeeData.state_cooperative_number,
            bank_account_number: employeeData.bank_account_number
          })
        
        if (employeeError) {
          // Rollback user creation
          await supabase.from('users').delete().eq('id', userData.id)
          errors.push(`Failed to create employee record for ${employeeData.username}: ${employeeError.message}`)
          continue
        }
        
        importedEmployees.push({
          username: employeeData.username,
          full_name: employeeData.full_name,
          id: userData.id
        })
        
      } catch (error) {
        errors.push(`Error processing ${employeeData.username}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
    
    return NextResponse.json({
      success: true,
      imported: importedEmployees.length,
      total: result.data.length,
      errors: errors,
      employees: importedEmployees
    })
    
  } catch (error) {
    console.error('Employee import error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
