import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { processStudentExcel } from '@/lib/excel'
import { requireAdmin } from '@/lib/auth'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    await requireAdmin()
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }
    
    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an Excel file (.xlsx or .xls)' },
        { status: 400 }
      )
    }
    
    // Process Excel file
    const result = await processStudentExcel(file)
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Failed to process Excel file',
          details: result.errors
        },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const importedStudents = []
    const errors = []
    
    // Process each student
    for (const studentData of result.data) {
      try {
        // Hash password
        const hashedPassword = await bcrypt.hash(studentData.password, 12)
        
        // Create user record
        const { data: userData, error: userError } = await supabase
          .from('users')
          .insert({
            username: studentData.username,
            full_name: studentData.full_name,
            phone: studentData.phone,
            password_hash: hashedPassword,
            role: 'student',
            is_active: studentData.is_active
          })
          .select()
          .single()
        
        if (userError) {
          errors.push(`Failed to create user ${studentData.username}: ${userError.message}`)
          continue
        }
        
        // Create student record
        const { error: studentError } = await supabase
          .from('students')
          .insert({
            id: userData.id,
            nationality: studentData.nationality,
            specialization: studentData.specialization,
            section: studentData.section,
            class: studentData.class,
            level: studentData.level,
            result: studentData.result
          })
        
        if (studentError) {
          // Rollback user creation
          await supabase.from('users').delete().eq('id', userData.id)
          errors.push(`Failed to create student record for ${studentData.username}: ${studentError.message}`)
          continue
        }
        
        importedStudents.push({
          username: studentData.username,
          full_name: studentData.full_name,
          id: userData.id
        })
        
      } catch (error) {
        errors.push(`Error processing ${studentData.username}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
    
    return NextResponse.json({
      success: true,
      imported: importedStudents.length,
      total: result.data.length,
      errors: errors,
      students: importedStudents
    })
    
  } catch (error) {
    console.error('Student import error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
