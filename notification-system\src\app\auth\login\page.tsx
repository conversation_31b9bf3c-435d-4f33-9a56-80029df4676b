'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { getRedirectPath } from '@/lib/auth'
import toast from 'react-hot-toast'
import { Eye, EyeOff, LogIn } from 'lucide-react'

export default function LoginPage() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      console.log('🔍 Login attempt with username:', username)

      // First, get user by username to find their email
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, email, role, is_active, username')
        .eq('username', username)
        .single()

      console.log('🔍 User lookup result:', { userData, userError })

      if (userError || !userData) {
        console.log('❌ User not found in database')
        toast.error('Invalid username or password')
        return
      }

      if (!userData.is_active) {
        console.log('❌ User account is inactive')
        toast.error('Your account has been deactivated. Please contact an administrator.')
        return
      }

      // Use email for Supabase auth if available, otherwise use username
      const loginIdentifier = userData.email || username
      console.log('🔍 Attempting auth with identifier:', loginIdentifier)

      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: loginIdentifier,
        password: password,
      })

      // If email login fails and we have an email, try with the email directly
      if (error && userData.email && loginIdentifier !== userData.email) {
        console.log('🔍 Retrying with direct email:', userData.email)
        const { data: retryAuthData, error: retryError } = await supabase.auth.signInWithPassword({
          email: userData.email,
          password: password,
        })

        if (!retryError) {
          console.log('✅ Retry login successful!')
          toast.success('Login successful!')
          const redirectPath = getRedirectPath(userData.role)
          console.log('🔍 Redirecting to:', redirectPath)
          router.push(redirectPath)
          return
        }
      }

      console.log('🔍 Auth result:', { authData, error })

      if (error) {
        console.log('❌ Auth error:', error.message)
        toast.error(`Authentication failed: ${error.message}`)
        return
      }

      console.log('✅ Login successful!')
      toast.success('Login successful!')

      // Redirect based on user role
      const redirectPath = getRedirectPath(userData.role)
      console.log('🔍 Redirecting to:', redirectPath)
      router.push(redirectPath)

    } catch (error) {
      console.error('❌ Login error:', error)
      toast.error('An error occurred during login')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <LogIn className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Notification System
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleLogin}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="username" className="sr-only">
                Username
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
            </div>
            <div className="relative">
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                'Sign in'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
