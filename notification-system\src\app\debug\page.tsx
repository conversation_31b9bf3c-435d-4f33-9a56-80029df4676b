'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

export default function DebugPage() {
  const [users, setUsers] = useState<any[]>([])
  const [authUsers, setAuthUsers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    async function fetchData() {
      try {
        // Get users from public.users table
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('*')

        console.log('Users table data:', { usersData, usersError })
        setUsers(usersData || [])

        // Try to get current auth user
        const { data: { user }, error: authError } = await supabase.auth.getUser()
        console.log('Current auth user:', { user, authError })

      } catch (error) {
        console.error('Debug fetch error:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return <div className="p-8">Loading debug info...</div>
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Information</h1>
      
      <div className="space-y-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Users Table ({users.length} records)</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-300">
              <thead className="bg-gray-50">
                <tr>
                  <th className="border border-gray-300 px-4 py-2 text-left">ID</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Username</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Email</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Role</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Active</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Full Name</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id}>
                    <td className="border border-gray-300 px-4 py-2 text-xs">{user.id}</td>
                    <td className="border border-gray-300 px-4 py-2">{user.username}</td>
                    <td className="border border-gray-300 px-4 py-2">{user.email}</td>
                    <td className="border border-gray-300 px-4 py-2">{user.role}</td>
                    <td className="border border-gray-300 px-4 py-2">{user.is_active ? '✅' : '❌'}</td>
                    <td className="border border-gray-300 px-4 py-2">{user.full_name}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {users.length === 0 && (
            <p className="text-red-600 mt-4">❌ No users found in the users table!</p>
          )}
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Environment Check</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Supabase URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing'}</p>
            <p><strong>Supabase Anon Key:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}</p>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="space-x-4">
            <a 
              href="/setup" 
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
            >
              Setup Admin User
            </a>
            <a 
              href="/auth/login" 
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Go to Login
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
