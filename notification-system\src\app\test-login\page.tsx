'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import toast from 'react-hot-toast'

export default function TestLoginPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('admin123456')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const supabase = createClient()

  const testDirectLogin = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      console.log('🔍 Testing direct login with email:', email)
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
      })

      console.log('🔍 Direct login result:', { data, error })

      if (error) {
        setResult({
          success: false,
          error: error.message,
          code: error.status
        })
        toast.error(`Login failed: ${error.message}`)
      } else {
        setResult({
          success: true,
          user: data.user,
          session: !!data.session
        })
        toast.success('Direct login successful!')
      }

    } catch (err) {
      console.error('❌ Test login error:', err)
      setResult({
        success: false,
        error: err instanceof Error ? err.message : 'Unknown error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const checkUsers = async () => {
    try {
      const { data: users, error } = await supabase
        .from('users')
        .select('*')

      console.log('🔍 Users in database:', { users, error })
      
      if (error) {
        toast.error(`Database error: ${error.message}`)
      } else {
        toast.success(`Found ${users?.length || 0} users in database`)
        setResult({
          success: true,
          users: users,
          count: users?.length || 0
        })
      }
    } catch (err) {
      console.error('❌ Check users error:', err)
    }
  }

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Login</h1>
      
      <div className="space-y-6">
        <div className="bg-blue-50 p-4 rounded border">
          <h3 className="font-semibold mb-4">Direct Email/Password Test</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Email:</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Password:</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border rounded"
              />
            </div>
            <button
              onClick={testDirectLogin}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Testing...' : 'Test Direct Login'}
            </button>
          </div>
        </div>

        <div className="bg-green-50 p-4 rounded border">
          <h3 className="font-semibold mb-4">Database Check</h3>
          <button
            onClick={checkUsers}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            Check Users in Database
          </button>
        </div>

        {result && (
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-semibold mb-2">Result:</h3>
            <pre className="text-sm overflow-auto whitespace-pre-wrap">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="space-x-4">
          <a href="/debug" className="text-blue-600 hover:underline">Debug Page</a>
          <a href="/setup" className="text-purple-600 hover:underline">Setup Page</a>
          <a href="/auth/login" className="text-green-600 hover:underline">Normal Login</a>
        </div>
      </div>
    </div>
  )
}
