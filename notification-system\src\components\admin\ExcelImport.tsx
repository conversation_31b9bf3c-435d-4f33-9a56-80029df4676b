'use client'

import { useState } from 'react'
import { Upload, FileSpreadsheet, AlertCircle, CheckCircle, X } from 'lucide-react'
import toast from 'react-hot-toast'

interface ImportResult {
  success: boolean
  imported: number
  total: number
  errors: string[]
  students?: any[]
  employees?: any[]
}

interface ExcelImportProps {
  type: 'students' | 'employees'
  onImportComplete?: (result: ImportResult) => void
}

export default function ExcelImport({ type, onImportComplete }: ExcelImportProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [showResult, setShowResult] = useState(false)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // Validate file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ]
      
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error('Please select a valid Excel file (.xlsx or .xls)')
        return
      }
      
      setFile(selectedFile)
      setImportResult(null)
      setShowResult(false)
    }
  }

  const handleUpload = async () => {
    if (!file) {
      toast.error('Please select a file first')
      return
    }

    setIsUploading(true)
    
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const endpoint = type === 'students' 
        ? '/api/admin/import/students'
        : '/api/admin/import/employees'
      
      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Import failed')
      }
      
      setImportResult(result)
      setShowResult(true)
      
      if (result.success && result.imported > 0) {
        toast.success(`Successfully imported ${result.imported} ${type}`)
      } else if (result.errors?.length > 0) {
        toast.error(`Import completed with ${result.errors.length} errors`)
      }
      
      onImportComplete?.(result)
      
    } catch (error) {
      console.error('Import error:', error)
      toast.error(error instanceof Error ? error.message : 'Import failed')
    } finally {
      setIsUploading(false)
    }
  }

  const clearFile = () => {
    setFile(null)
    setImportResult(null)
    setShowResult(false)
  }

  const getRequiredHeaders = () => {
    if (type === 'students') {
      return [
        'username', 'full_name', 'nationality', 'phone', 'specialization',
        'section', 'class', 'level', 'result', 'password', 'is_active'
      ]
    } else {
      return [
        'full_name', 'contract_type', 'employee_type', 'phone', 'job_status',
        'automatic_number', 'financial_number', 'state_cooperative_number',
        'bank_account_number', 'username', 'password', 'is_active'
      ]
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center mb-4">
        <FileSpreadsheet className="h-5 w-5 text-blue-600 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">
          Import {type === 'students' ? 'Students' : 'Employees'} from Excel
        </h3>
      </div>

      {/* Required Headers Info */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Required Excel Headers:</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs text-blue-800">
          {getRequiredHeaders().map((header) => (
            <code key={header} className="bg-blue-100 px-2 py-1 rounded">
              {header}
            </code>
          ))}
        </div>
      </div>

      {/* File Upload */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Excel File
        </label>
        <div className="flex items-center space-x-4">
          <input
            type="file"
            accept=".xlsx,.xls"
            onChange={handleFileChange}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          {file && (
            <button
              onClick={clearFile}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        {file && (
          <p className="mt-2 text-sm text-gray-600">
            Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
          </p>
        )}
      </div>

      {/* Upload Button */}
      <button
        onClick={handleUpload}
        disabled={!file || isUploading}
        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isUploading ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
        ) : (
          <Upload className="h-4 w-4 mr-2" />
        )}
        {isUploading ? 'Importing...' : 'Import Data'}
      </button>

      {/* Import Results */}
      {showResult && importResult && (
        <div className="mt-6 p-4 border rounded-lg">
          <div className="flex items-center mb-3">
            {importResult.success ? (
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            )}
            <h4 className="text-lg font-medium">Import Results</h4>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-600">Total Records</p>
              <p className="text-2xl font-bold text-gray-900">{importResult.total}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Successfully Imported</p>
              <p className="text-2xl font-bold text-green-600">{importResult.imported}</p>
            </div>
          </div>

          {importResult.errors && importResult.errors.length > 0 && (
            <div className="mt-4">
              <h5 className="text-sm font-medium text-red-800 mb-2">Errors:</h5>
              <div className="bg-red-50 border border-red-200 rounded-md p-3 max-h-40 overflow-y-auto">
                {importResult.errors.map((error, index) => (
                  <p key={index} className="text-sm text-red-700 mb-1">
                    {error}
                  </p>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
