'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { LogOut } from 'lucide-react'
import toast from 'react-hot-toast'

interface LogoutButtonProps {
  className?: string
  showText?: boolean
}

export default function LogoutButton({ className = '', showText = true }: LogoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleLogout = async () => {
    setIsLoading(true)
    
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        toast.error('Error signing out')
        return
      }
      
      toast.success('Signed out successfully')
      router.push('/auth/login')
    } catch (error) {
      console.error('Logout error:', error)
      toast.error('An error occurred during logout')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <button
      onClick={handleLogout}
      disabled={isLoading}
      className={`inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      {isLoading ? (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
      ) : (
        <LogOut className="h-4 w-4" />
      )}
      {showText && (
        <span className="ml-2">
          {isLoading ? 'Signing out...' : 'Sign out'}
        </span>
      )}
    </button>
  )
}
