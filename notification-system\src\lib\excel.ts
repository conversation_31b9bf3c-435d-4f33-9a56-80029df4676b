import * as XLSX from 'xlsx'
import { StudentImportData, EmployeeImportData, ContractType, EmployeeType, JobStatus } from '@/types/database'
import { generatePassword, validateEmail, validatePhone } from './utils'

export interface ImportResult<T> {
  success: boolean
  data: T[]
  errors: string[]
  warnings: string[]
}

export interface ValidationError {
  row: number
  field: string
  message: string
}

// Required headers for student import
export const STUDENT_HEADERS = [
  'username',
  'full_name', 
  'nationality',
  'phone',
  'specialization',
  'section',
  'class',
  'level',
  'result',
  'password',
  'is_active'
] as const

// Required headers for employee import
export const EMPLOYEE_HEADERS = [
  'full_name',
  'contract_type',
  'employee_type', 
  'phone',
  'job_status',
  'automatic_number',
  'financial_number',
  'state_cooperative_number',
  'bank_account_number',
  'username',
  'password',
  'is_active'
] as const

export function parseExcelFile(file: File): Promise<any[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        resolve(jsonData)
      } catch (error) {
        reject(error)
      }
    }
    
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}

export function validateStudentHeaders(headers: string[]): string[] {
  const errors: string[] = []
  const normalizedHeaders = headers.map(h => h.toLowerCase().trim())
  
  for (const requiredHeader of STUDENT_HEADERS) {
    if (!normalizedHeaders.includes(requiredHeader)) {
      errors.push(`Missing required header: ${requiredHeader}`)
    }
  }
  
  return errors
}

export function validateEmployeeHeaders(headers: string[]): string[] {
  const errors: string[] = []
  const normalizedHeaders = headers.map(h => h.toLowerCase().trim())
  
  for (const requiredHeader of EMPLOYEE_HEADERS) {
    if (!normalizedHeaders.includes(requiredHeader)) {
      errors.push(`Missing required header: ${requiredHeader}`)
    }
  }
  
  return errors
}

export function validateStudentRow(row: any, rowIndex: number): ValidationError[] {
  const errors: ValidationError[] = []
  
  // Required fields validation
  if (!row.username?.trim()) {
    errors.push({ row: rowIndex, field: 'username', message: 'Username is required' })
  }
  
  if (!row.full_name?.trim()) {
    errors.push({ row: rowIndex, field: 'full_name', message: 'Full name is required' })
  }
  
  if (!row.password?.trim()) {
    errors.push({ row: rowIndex, field: 'password', message: 'Password is required' })
  }
  
  // Phone validation
  if (row.phone && !validatePhone(row.phone)) {
    errors.push({ row: rowIndex, field: 'phone', message: 'Invalid phone number format' })
  }
  
  // Boolean validation for is_active
  if (row.is_active !== undefined && typeof row.is_active !== 'boolean' && 
      !['true', 'false', '1', '0', 'yes', 'no'].includes(String(row.is_active).toLowerCase())) {
    errors.push({ row: rowIndex, field: 'is_active', message: 'is_active must be true/false or 1/0 or yes/no' })
  }
  
  return errors
}

export function validateEmployeeRow(row: any, rowIndex: number): ValidationError[] {
  const errors: ValidationError[] = []
  
  // Required fields validation
  if (!row.username?.trim()) {
    errors.push({ row: rowIndex, field: 'username', message: 'Username is required' })
  }
  
  if (!row.full_name?.trim()) {
    errors.push({ row: rowIndex, field: 'full_name', message: 'Full name is required' })
  }
  
  if (!row.password?.trim()) {
    errors.push({ row: rowIndex, field: 'password', message: 'Password is required' })
  }
  
  // Phone validation
  if (row.phone && !validatePhone(row.phone)) {
    errors.push({ row: rowIndex, field: 'phone', message: 'Invalid phone number format' })
  }
  
  // Contract type validation
  const validContractTypes: ContractType[] = ['full_time', 'part_time', 'contract', 'temporary']
  if (row.contract_type && !validContractTypes.includes(row.contract_type)) {
    errors.push({ row: rowIndex, field: 'contract_type', message: `Invalid contract type. Must be one of: ${validContractTypes.join(', ')}` })
  }
  
  // Employee type validation
  const validEmployeeTypes: EmployeeType[] = ['academic', 'administrative', 'technical', 'support']
  if (row.employee_type && !validEmployeeTypes.includes(row.employee_type)) {
    errors.push({ row: rowIndex, field: 'employee_type', message: `Invalid employee type. Must be one of: ${validEmployeeTypes.join(', ')}` })
  }
  
  // Job status validation
  const validJobStatuses: JobStatus[] = ['active', 'inactive', 'on_leave', 'terminated']
  if (row.job_status && !validJobStatuses.includes(row.job_status)) {
    errors.push({ row: rowIndex, field: 'job_status', message: `Invalid job status. Must be one of: ${validJobStatuses.join(', ')}` })
  }
  
  // Boolean validation for is_active
  if (row.is_active !== undefined && typeof row.is_active !== 'boolean' && 
      !['true', 'false', '1', '0', 'yes', 'no'].includes(String(row.is_active).toLowerCase())) {
    errors.push({ row: rowIndex, field: 'is_active', message: 'is_active must be true/false or 1/0 or yes/no' })
  }
  
  return errors
}

export function normalizeStudentData(row: any): StudentImportData {
  return {
    username: row.username?.trim(),
    full_name: row.full_name?.trim(),
    nationality: row.nationality?.trim() || undefined,
    phone: row.phone?.trim() || undefined,
    specialization: row.specialization?.trim() || undefined,
    section: row.section?.trim() || undefined,
    class: row.class?.trim() || undefined,
    level: row.level?.trim() || undefined,
    result: row.result?.trim() || undefined,
    password: row.password?.trim() || generatePassword(),
    is_active: parseBooleanValue(row.is_active)
  }
}

export function normalizeEmployeeData(row: any): EmployeeImportData {
  return {
    full_name: row.full_name?.trim(),
    contract_type: row.contract_type?.trim() as ContractType || undefined,
    employee_type: row.employee_type?.trim() as EmployeeType || undefined,
    phone: row.phone?.trim() || undefined,
    job_status: row.job_status?.trim() as JobStatus || 'active',
    automatic_number: row.automatic_number?.trim() || undefined,
    financial_number: row.financial_number?.trim() || undefined,
    state_cooperative_number: row.state_cooperative_number?.trim() || undefined,
    bank_account_number: row.bank_account_number?.trim() || undefined,
    username: row.username?.trim(),
    password: row.password?.trim() || generatePassword(),
    is_active: parseBooleanValue(row.is_active)
  }
}

function parseBooleanValue(value: any): boolean {
  if (typeof value === 'boolean') return value
  if (typeof value === 'number') return value === 1
  if (typeof value === 'string') {
    const lower = value.toLowerCase().trim()
    return ['true', '1', 'yes', 'y'].includes(lower)
  }
  return true // default to true
}

export async function processStudentExcel(file: File): Promise<ImportResult<StudentImportData>> {
  try {
    const rawData = await parseExcelFile(file)
    
    if (rawData.length < 2) {
      return {
        success: false,
        data: [],
        errors: ['File must contain at least a header row and one data row'],
        warnings: []
      }
    }
    
    const headers = rawData[0] as string[]
    const headerErrors = validateStudentHeaders(headers)
    
    if (headerErrors.length > 0) {
      return {
        success: false,
        data: [],
        errors: headerErrors,
        warnings: []
      }
    }
    
    const dataRows = rawData.slice(1)
    const processedData: StudentImportData[] = []
    const errors: string[] = []
    const warnings: string[] = []
    
    // Create header mapping
    const headerMap: { [key: string]: number } = {}
    headers.forEach((header, index) => {
      headerMap[header.toLowerCase().trim()] = index
    })
    
    dataRows.forEach((row, index) => {
      const rowIndex = index + 2 // +2 because we start from row 2 (after header)
      
      // Convert array row to object using header mapping
      const rowObject: any = {}
      STUDENT_HEADERS.forEach(header => {
        const columnIndex = headerMap[header]
        rowObject[header] = row[columnIndex]
      })
      
      const validationErrors = validateStudentRow(rowObject, rowIndex)
      
      if (validationErrors.length > 0) {
        validationErrors.forEach(error => {
          errors.push(`Row ${error.row}, ${error.field}: ${error.message}`)
        })
      } else {
        processedData.push(normalizeStudentData(rowObject))
      }
    })
    
    return {
      success: errors.length === 0,
      data: processedData,
      errors,
      warnings
    }
  } catch (error) {
    return {
      success: false,
      data: [],
      errors: [`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings: []
    }
  }
}

export async function processEmployeeExcel(file: File): Promise<ImportResult<EmployeeImportData>> {
  try {
    const rawData = await parseExcelFile(file)
    
    if (rawData.length < 2) {
      return {
        success: false,
        data: [],
        errors: ['File must contain at least a header row and one data row'],
        warnings: []
      }
    }
    
    const headers = rawData[0] as string[]
    const headerErrors = validateEmployeeHeaders(headers)
    
    if (headerErrors.length > 0) {
      return {
        success: false,
        data: [],
        errors: headerErrors,
        warnings: []
      }
    }
    
    const dataRows = rawData.slice(1)
    const processedData: EmployeeImportData[] = []
    const errors: string[] = []
    const warnings: string[] = []
    
    // Create header mapping
    const headerMap: { [key: string]: number } = {}
    headers.forEach((header, index) => {
      headerMap[header.toLowerCase().trim()] = index
    })
    
    dataRows.forEach((row, index) => {
      const rowIndex = index + 2 // +2 because we start from row 2 (after header)
      
      // Convert array row to object using header mapping
      const rowObject: any = {}
      EMPLOYEE_HEADERS.forEach(header => {
        const columnIndex = headerMap[header]
        rowObject[header] = row[columnIndex]
      })
      
      const validationErrors = validateEmployeeRow(rowObject, rowIndex)
      
      if (validationErrors.length > 0) {
        validationErrors.forEach(error => {
          errors.push(`Row ${error.row}, ${error.field}: ${error.message}`)
        })
      } else {
        processedData.push(normalizeEmployeeData(rowObject))
      }
    })
    
    return {
      success: errors.length === 0,
      data: processedData,
      errors,
      warnings
    }
  } catch (error) {
    return {
      success: false,
      data: [],
      errors: [`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings: []
    }
  }
}
