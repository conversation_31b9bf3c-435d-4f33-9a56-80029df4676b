export type UserRole = 'admin' | 'student' | 'employee'
export type ContractType = 'full_time' | 'part_time' | 'contract' | 'temporary'
export type EmployeeType = 'academic' | 'administrative' | 'technical' | 'support'
export type JobStatus = 'active' | 'inactive' | 'on_leave' | 'terminated'

export interface User {
  id: string
  username: string
  email?: string
  password_hash?: string  // Optional since we use Supabase auth
  full_name: string
  phone?: string
  role: UserRole
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Student {
  id: string
  nationality?: string
  specialization?: string
  section?: string
  class?: string
  level?: string
  result?: string
  student_number?: string
  enrollment_date?: string
  user?: User
}

export interface Employee {
  id: string
  contract_type?: ContractType
  employee_type?: EmployeeType
  job_status?: JobStatus
  automatic_number?: string
  financial_number?: string
  state_cooperative_number?: string
  bank_account_number?: string
  employee_number?: string
  hire_date?: string
  department?: string
  user?: User
}

export interface Notification {
  id: string
  title: string
  message: string
  created_by: string
  created_at: string
  updated_at: string
  is_active: boolean
  creator?: User
  attachments?: NotificationAttachment[]
  recipients?: NotificationRecipient[]
}

export interface NotificationAttachment {
  id: string
  notification_id: string
  file_name: string
  file_path: string
  file_size?: number
  mime_type?: string
  uploaded_at: string
}

export interface NotificationRecipient {
  id: string
  notification_id: string
  user_id: string
  is_read: boolean
  read_at?: string
  created_at: string
  user?: User
}

export interface RecipientGroup {
  id: string
  notification_id: string
  group_type: string
  group_value?: string
  created_at: string
}

// Excel import interfaces
export interface StudentImportData {
  username: string
  full_name: string
  nationality?: string
  phone?: string
  specialization?: string
  section?: string
  class?: string
  level?: string
  result?: string
  password: string
  is_active: boolean
}

export interface EmployeeImportData {
  full_name: string
  contract_type?: ContractType
  employee_type?: EmployeeType
  phone?: string
  job_status?: JobStatus
  automatic_number?: string
  financial_number?: string
  state_cooperative_number?: string
  bank_account_number?: string
  username: string
  password: string
  is_active: boolean
}

// Notification creation interfaces
export interface NotificationCreateData {
  title: string
  message: string
  recipients: {
    type: 'all' | 'students' | 'employees' | 'by_class' | 'by_level' | 'by_contract_type' | 'by_job_status'
    value?: string
  }[]
  attachments?: File[]
}

// Dashboard data interfaces
export interface DashboardStats {
  totalUsers: number
  totalStudents: number
  totalEmployees: number
  totalNotifications: number
  unreadNotifications: number
  recentNotifications: Notification[]
}

export interface UserProfile {
  user: User
  student?: Student
  employee?: Employee
  unreadCount: number
  recentNotifications: Notification[]
}
